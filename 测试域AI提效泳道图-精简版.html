<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效泳道图 - 基于上下文工程</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            padding: 25px;
            overflow-x: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .core-concept {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            font-size: 14px;
        }

        /* 泳道图主体 */
        .swimlane-diagram {
            display: grid;
            grid-template-columns: 180px repeat(4, 1fr);
            grid-template-rows: repeat(3, auto);
            gap: 2px;
            background: #ecf0f1;
            border-radius: 10px;
            padding: 2px;
            min-width: 1400px;
            margin-top: 30px;
        }

        /* 泳道标题列 */
        .lane-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
            border-radius: 8px;
            writing-mode: horizontal-tb;
        }

        /* 流程阶段标题行 */
        .phase-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 18px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 泳道单元格 */
        .lane-cell {
            background: white;
            padding: 15px;
            border-radius: 8px;
            min-height: 100px;
            position: relative;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .lane-cell:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }

        /* 用户触点层样式 */
        .user-lane .lane-cell {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* 上下文工程层样式 - 核心层 */
        .context-lane .lane-cell {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: 3px solid #ff6b9d;
        }

        /* LLM基础设施层样式 */
        .llm-lane .lane-cell {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .cell-title {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 8px;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            padding-bottom: 4px;
        }

        .cell-content {
            font-size: 11px;
            line-height: 1.4;
            opacity: 0.9;
        }

        /* 流程箭头 */
        .flow-arrow {
            position: absolute;
            right: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 12px solid #3498db;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            z-index: 10;
        }

        .lane-cell:last-child .flow-arrow {
            display: none;
        }

        /* AI提效标识 */
        .ai-enhancement {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            font-weight: bold;
        }

        /* 核心引擎标识 */
        .core-engine {
            position: absolute;
            top: -10px;
            left: -10px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        /* 关键特性标识 */
        .key-feature {
            background: rgba(255,255,255,0.2);
            border: 1px dashed rgba(255,255,255,0.5);
            border-radius: 4px;
            padding: 3px 6px;
            font-size: 9px;
            margin-top: 5px;
            display: inline-block;
        }

        /* 图例 */
        .legend {
            margin-top: 25px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .legend-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .legend-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 18px;
            height: 18px;
            border-radius: 4px;
        }

        .legend-text {
            font-size: 13px;
            color: #495057;
        }

        /* 响应式设计 */
        @media (max-width: 1600px) {
            .container {
                padding: 20px;
            }
            .swimlane-diagram {
                min-width: 1200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">测试域AI提效泳道图</div>
            <div class="subtitle">基于上下文工程的智能测试新范式</div>
            <div class="core-concept">
                🧠 核心：以正确格式向模型传递正确的信息、指令和工具，实现可靠的任务完成
            </div>
        </div>

        <!-- 泳道图主体 -->
        <div class="swimlane-diagram">
            <!-- 空白角落 -->
            <div class="phase-header" style="background: #95a5a6;">层级/阶段</div>

            <!-- 流程阶段标题 -->
            <div class="phase-header">📋 用例设计</div>
            <div class="phase-header">🔧 自动化开发</div>
            <div class="phase-header">🚀 执行部署</div>
            <div class="phase-header">🔍 故障复盘</div>

            <!-- 用户触点层 -->
            <div class="lane-header">用户触点层<br><small>个人智能体平台</small></div>
            <div class="lane-cell user-lane">
                <div class="cell-title">💻 Cursor</div>
                <div class="ai-enhancement">AI</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell user-lane">
                <div class="cell-title">🤖 AI IDE</div>
                <div class="ai-enhancement">AI</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell user-lane">
                <div class="cell-title">🍒 Cherry Studio</div>
                <div class="ai-enhancement">AI</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell user-lane">
                <div class="cell-title">💬 ChatBox</div>
                <div class="ai-enhancement">AI</div>
            </div>

            <!-- 上下文工程层 - 核心层 -->
            <div class="lane-header">上下文工程层<br><small>🧠 智能核心引擎</small></div>
            <div class="lane-cell context-lane">
                <div class="core-engine">🧠</div>
                <div class="cell-title">📝 提示词+画像</div>
                <div class="cell-content">
                    • 用例设计模板<br>
                    • 用户技术栈偏好<br>
                    • 个性化提示词<br>
                    • 工作习惯建模
                </div>
                <div class="key-feature">智能理解</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell context-lane">
                <div class="core-engine">🧠</div>
                <div class="cell-title">🧠 记忆+检索</div>
                <div class="cell-content">
                    • 开发历史记忆<br>
                    • 技术文档检索<br>
                    • 框架知识库<br>
                    • RAG增强生成
                </div>
                <div class="key-feature">知识融合</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell context-lane">
                <div class="core-engine">🧠</div>
                <div class="cell-title">🛠️ MCP工具链</div>
                <div class="cell-content">
                    • iCenter MCP<br>
                    • RDC MCP<br>
                    • 自动化框架MCP<br>
                    • SequentialThinking
                </div>
                <div class="key-feature">工具集成</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell context-lane">
                <div class="core-engine">🧠</div>
                <div class="cell-title">💾 知识沉淀</div>
                <div class="cell-content">
                    • 故障模式总结<br>
                    • 解决方案归档<br>
                    • 私域知识共享<br>
                    • 组织学习促进
                </div>
                <div class="key-feature">经验复用</div>
            </div>

            <!-- LLM基础设施层 -->
            <div class="lane-header">LLM基础设施层<br><small>AI能力底座</small></div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">🤖 模型服务</div>
                <div class="cell-content">
                    • 星云大模型<br>
                    • 电信大模型<br>
                    • Qwen系列<br>
                    • 测试域小模型
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">⚙️ 模型调优</div>
                <div class="cell-content">
                    • 精调优化<br>
                    • 量化压缩<br>
                    • 模型蒸馏<br>
                    • 强化学习RLHF
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">📊 性能监控</div>
                <div class="cell-content">
                    • 推理延迟监控<br>
                    • 资源使用优化<br>
                    • 服务可用性<br>
                    • 质量持续评估
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">📈 效果评估</div>
                <div class="cell-content">
                    • F1/Recall/Accuracy<br>
                    • 莱文斯坦距离<br>
                    • 用户满意度<br>
                    • 持续改进反馈
                </div>
            </div>
        </div>

        <!-- 图例说明 -->
        <div class="legend">
            <div class="legend-title">🔍 图例说明</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                    <div class="legend-text">用户触点层 - 个人智能体平台</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: 2px solid #ff6b9d;"></div>
                    <div class="legend-text">上下文工程层 - 智能核心引擎 🧠</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);"></div>
                    <div class="legend-text">LLM基础设施层 - AI能力底座</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12; border-radius: 50%;"></div>
                    <div class="legend-text">AI智能提效标识</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c; border-radius: 50%;"></div>
                    <div class="legend-text">🧠 核心引擎标识</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
