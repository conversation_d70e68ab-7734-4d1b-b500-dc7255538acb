<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效业务架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            padding: 20px;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .title {
            font-size: 26px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 12px;
        }

        .core-concept {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            font-size: 14px;
        }

        /* 业务架构布局 */
        .business-architecture {
            display: grid;
            grid-template-rows: auto auto auto auto;
            gap: 15px;
            margin-top: 25px;
        }

        .layer {
            background: white;
            border-radius: 12px;
            padding: 18px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .layer:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.15);
        }

        /* 用户触点层 */
        .touchpoint-layer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #5a67d8;
        }

        /* 业务流层 */
        .business-flow-layer {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-color: #ff6b9d;
            border: 3px solid #ff6b9d;
        }

        /* 上下文工程层 */
        .context-layer {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            border-color: #38f9d7;
        }

        /* 基础设施层 */
        .infrastructure-layer {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            border-color: #fa709a;
        }

        .layer-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .layer-description {
            font-size: 12px;
            margin-bottom: 15px;
            padding: 8px 12px;
            background: rgba(255,255,255,0.25);
            border: 1px dashed rgba(255,255,255,0.5);
            border-radius: 6px;
            font-weight: bold;
            opacity: 0.9;
        }

        .layer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .component {
            background: rgba(255,255,255,0.15);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .component-title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .component-items {
            font-size: 11px;
            line-height: 1.4;
            opacity: 0.9;
        }

        .component-items li {
            margin-bottom: 3px;
        }

        /* 连接线和箭头 */
        .connection {
            position: absolute;
            width: 2px;
            background: linear-gradient(to bottom, #3498db, #e74c3c);
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
        }

        .arrow-down {
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #e74c3c;
        }

        /* 特殊标识 */
        .ai-badge {
            position: absolute;
            top: -12px;
            right: -12px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        /* 业务流特殊样式 */
        .flow-step {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }

        .flow-step::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 用户触点层特殊样式 */
        .touchpoint-layer .component {
            padding: 12px;
            text-align: center;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .touchpoint-layer .component-title {
            margin-bottom: 0;
            font-size: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .layer-content {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            .layer-content {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .layer-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">测试域AI提效业务架构图</div>
            <div class="subtitle">四层架构：用户触点 → 业务流 → 上下文工程 → 基础设施</div>
            <div class="core-concept">
                业务驱动的智能化测试提效解决方案
            </div>
        </div>

        <div class="business-architecture">
            <!-- 用户触点层 -->
            <div class="layer touchpoint-layer">
                <div class="layer-title">
                    🖥️ 用户触点层
                </div>
                <div class="layer-description">
                    多元化交互入口，支持不同使用场景和用户偏好
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">💻 Cursor IDE</div>
                    </div>
                    <div class="component">
                        <div class="component-title">🤖 AI IDE</div>
                    </div>
                    <div class="component">
                        <div class="component-title">🍒 Cherry Studio</div>
                    </div>
                    <div class="component">
                        <div class="component-title">💬 ChatBox</div>
                    </div>
                </div>
                <div class="connection" style="height: 15px; bottom: -15px;"></div>
                <div class="arrow-down"></div>
            </div>

            <!-- 上下文工程层 -->
            <div class="layer context-layer">
                <div class="layer-title">
                    🎯 上下文工程层
                </div>
                <div class="layer-description">
                    核心上下文处理能力，为AI提供丰富的决策依据和执行工具
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">📝 提示词模板</div>
                        <div class="component-items">
                            • 场景化模板库<br>
                            • 动态参数注入<br>
                            • 模板版本管理<br>
                            • 效果反馈优化
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">👤 用户画像</div>
                        <div class="component-items">
                            • 技术栈偏好<br>
                            • 工作习惯分析<br>
                            • 能力水平评估<br>
                            • 个性化推荐
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🧠 短期记忆</div>
                        <div class="component-items">
                            • 对话历史摘要<br>
                            • 会话上下文<br>
                            • 任务状态跟踪<br>
                            • 实时信息缓存
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">💾 长期记忆</div>
                        <div class="component-items">
                            • 用户偏好沉淀<br>
                            • 历史交互记录<br>
                            • 成功案例库<br>
                            • 知识图谱构建
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🔍 检索信息</div>
                        <div class="component-items">
                            • 网页实时检索<br>
                            • 文档知识检索<br>
                            • 技术资料查询<br>
                            • 最佳实践搜索
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">📚 RAG信息</div>
                        <div class="component-items">
                            • 测试用例知识库<br>
                            • 故障案例库<br>
                            • 技术文档库<br>
                            • 向量化存储检索
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🛠️ MCP信息</div>
                        <div class="component-items">
                            • SequentialThinking MCP<br>
                            • Memory MCP<br>
                            • FileSystem MCP<br>
                            • iCenter MCP<br>
                            • RDC MCP<br>
                            • 自动化框架MCP
                        </div>
                    </div>
                </div>
                <div class="connection" style="height: 15px; bottom: -15px;"></div>
                <div class="arrow-down"></div>
            </div>

            <!-- 基础设施层 -->
            <div class="layer infrastructure-layer">
                <div class="layer-title">
                    🏗️ 基础设施层 - AI能力底座
                </div>
                <div class="layer-description">
                    提供强大的AI计算能力、模型服务和质量保障体系
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">🧠 模型服务</div>
                        <div class="component-items">
                            • 星云大模型<br>
                            • 电信大模型<br>
                            • Qwen系列模型<br>
                            • Deepseek模型<br>
                            • 测试域专用小模型
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">⚙️ 模型调优</div>
                        <div class="component-items">
                            • 精调（Fine-tuning）<br>
                            • 量化压缩<br>
                            • 模型蒸馏<br>
                            • 强化学习（RLHF）<br>
                            • Post-pretraining
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">📊 效果评估</div>
                        <div class="component-items">
                            • 莱文斯坦距离<br>
                            • F1 Score<br>
                            • Recall召回率<br>
                            • Accuracy准确率<br>
                            • Loss损失函数
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🔄 持续优化</div>
                        <div class="component-items">
                            • 模型性能监控<br>
                            • A/B测试验证<br>
                            • 自动化调优<br>
                            • 反馈循环机制
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 架构特点说明 -->
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea;">
            <h3 style="color: #2c3e50; margin-bottom: 10px; font-size: 16px;">🏗️ 业务架构特点</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; font-size: 12px;">
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #667eea; margin-bottom: 8px;">🔄 端到端业务闭环</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        从用户输入到知识沉淀的完整业务流程，确保每个环节都有明确的价值输出和质量保障
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #f093fb; margin-bottom: 8px;">🎯 场景化智能处理</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        针对不同测试场景提供专门的处理流程，通过场景识别自动匹配最优的执行策略
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #43e97b; margin-bottom: 8px;">🧠 丰富上下文支撑</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        多维度上下文信息融合，包括用户画像、记忆系统、知识库等，为AI决策提供充分依据
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #fa709a; margin-bottom: 8px;">🏗️ 强大基础设施</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        多模型支持、持续调优、科学评估，构建稳定可靠的AI能力底座
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

            <!-- 业务流层 -->
            <div class="layer business-flow-layer">
                <div class="ai-badge">🧠</div>
                <div class="layer-title">
                    ⚡ 业务流层
                </div>
                <div class="layer-description">
                    端到端的业务处理流程，从用户输入到知识沉淀的完整闭环
                </div>
                <div class="layer-content">
                    <div class="component flow-step">
                        <div class="component-title">📝 用户输入</div>
                        <div class="component-items">
                            • 文本输入<br>
                            • 文件上传<br>
                            • 图片识别
                        </div>
                    </div>
                    <div class="component flow-step">
                        <div class="component-title">🎯 场景识别</div>
                        <div class="component-items">
                            • 用例设计<br>
                            • 缺陷分析<br>
                            • 测试报告<br>
                            • 自动化脚本
                        </div>
                    </div>
                    <div class="component flow-step">
                        <div class="component-title">🧠 智能规划</div>
                        <div class="component-items">
                            • 任务分解<br>
                            • 步骤规划<br>
                            • 策略制定<br>
                            • 风险识别
                        </div>
                    </div>
                    <div class="component flow-step">
                        <div class="component-title">🔍 上下文收集</div>
                        <div class="component-items">
                            • 网页检索<br>
                            • 知识库查询<br>
                            • 历史案例<br>
                            • 用户画像
                        </div>
                    </div>
                    <div class="component flow-step">
                        <div class="component-title">✨ 智能生成</div>
                        <div class="component-items">
                            • 内容生成<br>
                            • 结果优化<br>
                            • 质量检查<br>
                            • 格式化输出
                        </div>
                    </div>
                    <div class="component flow-step">
                        <div class="component-title">🔄 交互优化</div>
                        <div class="component-items">
                            • 用户反馈<br>
                            • 迭代改进<br>
                            • 满意度确认<br>
                            • 结果调教
                        </div>
                    </div>
                    <div class="component flow-step">
                        <div class="component-title">💾 知识沉淀</div>
                        <div class="component-items">
                            • 经验记录<br>
                            • 模板优化<br>
                            • 知识共享<br>
                            • 持续学习
                        </div>
                    </div>
                </div>
                <div class="connection" style="height: 15px; bottom: -15px;"></div>
                <div class="arrow-down"></div>
            </div>
