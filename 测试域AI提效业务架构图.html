<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效业务架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 12px 30px rgba(0,0,0,0.2);
            padding: 15px;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 12px;
        }

        .title {
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 12px;
        }

        .core-concept {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            font-size: 14px;
        }

        /* 业务架构布局 */
        .business-architecture {
            display: grid;
            grid-template-rows: auto auto auto auto;
            gap: 10px;
            margin-top: 15px;
        }

        .layer {
            background: white;
            border-radius: 10px;
            padding: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .layer:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.15);
        }

        /* 用户触点层 */
        .touchpoint-layer {
            background: linear-gradient(135deg, #805ad5 0%, #553c9a 100%);
            color: white;
            border-color: #805ad5;
        }

        /* 业务流层 */
        .business-flow-layer {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
            border-color: #e53e3e;
            border: 3px solid #e53e3e;
        }

        /* 上下文工程层 */
        .context-layer {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
            color: white;
            border-color: #38a169;
        }

        /* 基础设施层 */
        .infrastructure-layer {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            border-color: #3182ce;
        }

        .layer-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .layer-description {
            font-size: 11px;
            margin-bottom: 10px;
            padding: 6px 10px;
            background: rgba(255,255,255,0.3);
            border: 1px dashed rgba(255,255,255,0.6);
            border-radius: 5px;
            font-weight: bold;
            opacity: 1;
            color: rgba(255,255,255,0.95);
        }

        .layer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 10px;
        }

        /* 业务流层和上下文工程层卡片一行显示 */
        .business-flow-layer .layer-content {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            gap: 8px;
            padding-bottom: 3px;
        }

        .business-flow-layer .component {
            flex: 0 0 auto;
            min-width: 140px;
            max-width: 160px;
        }

        .context-layer .layer-content {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            gap: 8px;
            padding-bottom: 3px;
        }

        .context-layer .component {
            flex: 0 0 auto;
            min-width: 140px;
            max-width: 160px;
        }

        /* 业务流和上下文工程层特殊布局 */
        .flow-context-container {
            display: grid;
            grid-template-rows: auto auto;
            gap: 10px;
            margin-bottom: 5px;
        }

        .component {
            background: rgba(255,255,255,0.25);
            padding: 8px;
            border-radius: 6px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .component-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .component-items {
            font-size: 10px;
            line-height: 1.3;
            opacity: 1;
            color: rgba(255,255,255,0.95);
        }

        .component-items li {
            margin-bottom: 3px;
        }

        /* 连接线和箭头 */
        .connection {
            position: absolute;
            width: 2px;
            background: linear-gradient(to bottom, #3498db, #e74c3c);
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
        }

        .arrow-down {
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #e74c3c;
        }

        /* 特殊标识 */
        .ai-badge {
            position: absolute;
            top: -12px;
            right: -12px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        /* 业务流特殊样式 */
        .flow-step {
            background: rgba(255,255,255,0.25);
            border: 1px solid rgba(255,255,255,0.3);
        }

        /* 用户触点层特殊样式 */
        .touchpoint-layer .component {
            padding: 8px;
            text-align: center;
            min-height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .touchpoint-layer .component-title {
            margin-bottom: 0;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .layer-content {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            }
            .business-flow-layer .component,
            .context-layer .component {
                min-width: 140px;
                max-width: 160px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            .layer-content {
                grid-template-columns: repeat(2, 1fr);
            }
            .business-flow-layer .layer-content,
            .context-layer .layer-content {
                flex-wrap: wrap;
                justify-content: center;
            }
            .business-flow-layer .component,
            .context-layer .component {
                min-width: 120px;
                max-width: 140px;
            }
        }

        @media (max-width: 480px) {
            .layer-content {
                grid-template-columns: 1fr;
            }
            .business-flow-layer .layer-content,
            .context-layer .layer-content {
                flex-direction: column;
                align-items: center;
            }
            .business-flow-layer .component,
            .context-layer .component {
                min-width: 200px;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">测试域AI提效业务架构图</div>
        </div>

        <div class="business-architecture">
            <!-- 用户触点层 -->
            <div class="layer touchpoint-layer">
                <div class="layer-title">
                    🖥️ 用户触点层
                </div>
                <div class="layer-description">
                    多元化交互入口，支持不同使用场景和用户偏好
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">💻 Cursor IDE</div>
                    </div>
                    <div class="component">
                        <div class="component-title">🤖 AI IDE</div>
                    </div>
                    <div class="component">
                        <div class="component-title">🍒 Cherry Studio</div>
                    </div>
                    <div class="component">
                        <div class="component-title">💬 ChatBox</div>
                    </div>
                </div>
                <div class="connection" style="height: 15px; bottom: -15px;"></div>
                <div class="arrow-down"></div>
            </div>

            <!-- 业务流层和上下文工程层并排 -->
            <div class="flow-context-container">
                <!-- 业务流层 -->
                <div class="layer business-flow-layer">
                    <div class="ai-badge">🧠</div>
                    <div class="layer-title">
                        ⚡ 业务流层
                    </div>
                    <div class="layer-description">
                        端到端的业务处理流程，从用户输入到知识沉淀的完整闭环
                    </div>
                    <div class="layer-content">
                        <div class="component flow-step">
                            <div class="component-title">📝 用户输入</div>
                            <div class="component-items">
                                • 文本输入<br>
                                • 文件上传<br>
                                • 图片识别
                            </div>
                        </div>
                        <div class="component flow-step">
                            <div class="component-title">🎯 场景识别</div>
                            <div class="component-items">
                                • 用例设计<br>
                                • 缺陷分析<br>
                                • 测试报告<br>
                                • 自动化脚本
                            </div>
                        </div>
                        <div class="component flow-step">
                            <div class="component-title">🧠 智能规划</div>
                            <div class="component-items">
                                • 任务分解<br>
                                • 步骤规划<br>
                                • 策略制定<br>
                                • 风险识别
                            </div>
                        </div>
                        <div class="component flow-step">
                            <div class="component-title">🔍 上下文收集</div>
                            <div class="component-items">
                                • 网页检索<br>
                                • 知识库查询<br>
                                • 历史案例<br>
                                • 用户画像
                            </div>
                        </div>
                        <div class="component flow-step">
                            <div class="component-title">✨ 智能生成</div>
                            <div class="component-items">
                                • 内容生成<br>
                                • 结果优化<br>
                                • 质量检查<br>
                                • 格式化输出
                            </div>
                        </div>
                        <div class="component flow-step">
                            <div class="component-title">🔄 交互优化</div>
                            <div class="component-items">
                                • 用户反馈<br>
                                • 迭代改进<br>
                                • 满意度确认<br>
                                • 结果调教
                            </div>
                        </div>
                        <div class="component flow-step">
                            <div class="component-title">💾 知识沉淀</div>
                            <div class="component-items">
                                • 经验记录<br>
                                • 模板优化<br>
                                • 知识共享<br>
                                • 持续学习
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 上下文工程层 -->
                <div class="layer context-layer">
                    <div class="layer-title">
                        🎯 上下文工程层
                    </div>
                    <div class="layer-description">
                        核心上下文处理能力，为AI提供丰富的决策依据和执行工具
                    </div>
                    <div class="layer-content">
                        <div class="component">
                            <div class="component-title">📝 提示词模板</div>
                            <div class="component-items">
                                • 场景化模板库<br>
                                • 动态参数注入<br>
                                • 模板版本管理<br>
                                • 效果反馈优化
                            </div>
                        </div>
                        <div class="component">
                            <div class="component-title">👤 用户画像</div>
                            <div class="component-items">
                                • 技术栈偏好<br>
                                • 工作习惯分析<br>
                                • 能力水平评估<br>
                                • 个性化推荐
                            </div>
                        </div>
                        <div class="component">
                            <div class="component-title">🧠 短期记忆</div>
                            <div class="component-items">
                                • 对话历史摘要<br>
                                • 会话上下文<br>
                                • 任务状态跟踪<br>
                                • 实时信息缓存
                            </div>
                        </div>
                        <div class="component">
                            <div class="component-title">💾 长期记忆</div>
                            <div class="component-items">
                                • 用户偏好沉淀<br>
                                • 历史交互记录<br>
                                • 成功案例库<br>
                                • 知识图谱构建
                            </div>
                        </div>
                        <div class="component">
                            <div class="component-title">🔍 检索信息</div>
                            <div class="component-items">
                                • 网页实时检索<br>
                                • 文档知识检索<br>
                                • 技术资料查询<br>
                                • 最佳实践搜索
                            </div>
                        </div>
                        <div class="component">
                            <div class="component-title">📚 RAG信息</div>
                            <div class="component-items">
                                • 测试用例知识库<br>
                                • 故障案例库<br>
                                • 技术文档库<br>
                                • 向量化存储检索
                            </div>
                        </div>
                        <div class="component">
                            <div class="component-title">🛠️ MCP信息</div>
                            <div class="component-items">
                                • SequentialThinking MCP<br>
                                • Memory MCP<br>
                                • FileSystem MCP<br>
                                • iCenter MCP<br>
                                • RDC MCP<br>
                                • 自动化框架MCP
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础设施层 -->
            <div class="layer infrastructure-layer">
                <div class="layer-title">
                    🏗️ 基础设施层
                </div>
                <div class="layer-description">
                    提供强大的AI计算能力、模型服务和质量保障体系
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">🧠 模型服务</div>
                        <div class="component-items">
                            • 星云大模型<br>
                            • 电信大模型<br>
                            • Qwen系列模型<br>
                            • Deepseek模型<br>
                            • 测试域专用小模型
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">⚙️ 模型调优</div>
                        <div class="component-items">
                            • 精调（Fine-tuning）<br>
                            • 量化压缩<br>
                            • 模型蒸馏<br>
                            • 强化学习（RLHF）<br>
                            • Post-pretraining
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">📊 效果评估</div>
                        <div class="component-items">
                            • 莱文斯坦距离<br>
                            • F1 Score<br>
                            • Recall召回率<br>
                            • Accuracy准确率<br>
                            • Loss损失函数
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 架构特点说明 -->
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea;">
            <h3 style="color: #2c3e50; margin-bottom: 10px; font-size: 16px;">🏗️ 业务架构特点</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; font-size: 12px;">
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #667eea; margin-bottom: 8px;">🔄 端到端业务闭环</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        从用户输入到知识沉淀的完整业务流程，确保每个环节都有明确的价值输出和质量保障
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #f093fb; margin-bottom: 8px;">🎯 场景化智能处理</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        针对不同测试场景提供专门的处理流程，通过场景识别自动匹配最优的执行策略
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #43e97b; margin-bottom: 8px;">🧠 丰富上下文支撑</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        多维度上下文信息融合，包括用户画像、记忆系统、知识库等，为AI决策提供充分依据
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #fa709a; margin-bottom: 8px;">🏗️ 强大基础设施</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        多模型支持、持续调优、科学评估，构建稳定可靠的AI能力底座
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
