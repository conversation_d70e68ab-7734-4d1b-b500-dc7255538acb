<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效技术架构 - 基于上下文工程</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            padding: 40px;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .core-concept {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            font-size: 16px;
        }

        /* 架构层级布局 */
        .architecture {
            display: grid;
            grid-template-rows: auto auto auto auto;
            gap: 30px;
            margin-top: 40px;
        }

        .layer {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .layer:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        /* 用户触点层 */
        .user-layer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #5a67d8;
        }

        /* 上下文工程层 - 核心层 */
        .context-layer {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: 3px solid #ff6b9d;
            position: relative;
        }

        .context-layer::before {
            content: "🧠 核心引擎";
            position: absolute;
            top: -15px;
            left: 30px;
            background: #ff6b9d;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }



        /* LLM基础设施层 */
        .llm-layer {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            border-color: #38f9d7;
        }

        /* 数据与评估层 */
        .data-layer {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            border-color: #fa709a;
        }

        .layer-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .layer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .component {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .component-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .component-items {
            font-size: 13px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .component-items li {
            margin-bottom: 5px;
        }

        /* 连接线和箭头 */
        .connection {
            position: absolute;
            width: 3px;
            background: linear-gradient(to bottom, #3498db, #e74c3c);
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
        }

        .arrow-down {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 15px solid #e74c3c;
        }

        /* 特殊标识 */
        .ai-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        .key-feature {
            background: rgba(255,255,255,0.25);
            border: 2px dashed rgba(255,255,255,0.5);
            border-radius: 8px;
            padding: 10px;
            margin-top: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .layer-content {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .layer-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">测试域AI提效技术架构</div>
            <div class="subtitle">基于上下文工程的智能测试新范式</div>
            <div class="core-concept">
                核心理念：以正确格式向模型传递正确的信息、指令和工具，实现可靠的任务完成
            </div>
        </div>

        <div class="architecture">
            <!-- 用户触点层 -->
            <div class="layer user-layer">
                <div class="layer-title">
                    🖥️ 用户触点层 - 个人通用智能体平台
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">💻 Cursor IDE</div>
                        <div class="component-items">
                            • 智能编程辅助<br>
                            • 代码生成与优化<br>
                            • 实时编程建议<br>
                            • 上下文感知编程
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🤖 AI IDE</div>
                        <div class="component-items">
                            • 集成开发环境<br>
                            • 智能脚本生成<br>
                            • 调试辅助工具<br>
                            • 版本管理集成
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🍒 Cherry Studio</div>
                        <div class="component-items">
                            • 对话式交互界面<br>
                            • 多模态输入支持<br>
                            • 智能问答系统<br>
                            • 实时状态查询
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">💬 ChatBox</div>
                        <div class="component-items">
                            • 轻量级交互工具<br>
                            • 快速问题咨询<br>
                            • 智能建议获取<br>
                            • 移动端支持
                        </div>
                    </div>
                </div>
                <div class="key-feature">
                    🔄 支持文本、文件、图片等多模态交互，用户可随时介入调教模型输出
                </div>
                <div class="connection" style="height: 30px; bottom: -30px;"></div>
                <div class="arrow-down"></div>
            </div>

            <!-- 上下文工程层 - 核心层 -->
            <div class="layer context-layer">
                <div class="ai-badge">🧠</div>
                <div class="layer-title">
                    🎯 上下文工程层 - 智能信息处理核心引擎
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">📝 提示词工程</div>
                        <div class="component-items">
                            • 用户个性化提示词<br>
                            • 通用提示词模板<br>
                            • 场景化提示词库<br>
                            • 动态提示词优化
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">👤 用户画像</div>
                        <div class="component-items">
                            • 技术栈偏好分析<br>
                            • 工作习惯建模<br>
                            • 能力水平评估<br>
                            • 个性化推荐引擎
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🧠 记忆系统</div>
                        <div class="component-items">
                            • 短期记忆：对话历史摘要<br>
                            • 长期记忆：用户偏好沉淀<br>
                            • 工作记忆：任务上下文<br>
                            • 知识记忆：专业领域知识
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🔍 检索增强</div>
                        <div class="component-items">
                            • 网页信息检索<br>
                            • 文档知识检索<br>
                            • 历史案例检索<br>
                            • 实时信息获取
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">📚 RAG信息</div>
                        <div class="component-items">
                            • 测试用例知识库<br>
                            • 故障案例库<br>
                            • 最佳实践库<br>
                            • 技术文档库
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🛠️ MCP工具链</div>
                        <div class="component-items">
                            • SequentialThinking MCP<br>
                            • Memory MCP<br>
                            • FileSystem MCP<br>
                            • iCenter MCP<br>
                            • RDC MCP<br>
                            • 自动化框架MCP
                        </div>
                    </div>
                </div>
                <div class="key-feature">
                    🎯 核心价值：将用户私域知识转化为共享知识，实现知识的自动沉淀与复用
                </div>
                <div class="connection" style="height: 30px; bottom: -30px;"></div>
                <div class="arrow-down"></div>
            </div>

            <!-- LLM基础设施层 -->
            <div class="layer llm-layer">
                <div class="layer-title">
                    🤖 LLM基础设施层 - AI能力底座
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">🧠 模型服务</div>
                        <div class="component-items">
                            • 星云大模型<br>
                            • 电信大模型<br>
                            • Qwen系列模型<br>
                            • Deepseek模型<br>
                            • 测试域专用小模型
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">⚙️ 模型调优</div>
                        <div class="component-items">
                            • 精调（Fine-tuning）<br>
                            • 量化压缩<br>
                            • 模型蒸馏<br>
                            • 强化学习（RLHF）<br>
                            • Post-pretraining
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🔧 推理优化</div>
                        <div class="component-items">
                            • 推理加速<br>
                            • 内存优化<br>
                            • 并发处理<br>
                            • 负载均衡<br>
                            • 缓存策略
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🛡️ 安全与治理</div>
                        <div class="component-items">
                            • 内容安全过滤<br>
                            • 隐私保护<br>
                            • 访问控制<br>
                            • 审计日志<br>
                            • 合规检查
                        </div>
                    </div>
                </div>
                <div class="connection" style="height: 30px; bottom: -30px;"></div>
                <div class="arrow-down"></div>
            </div>

            <!-- 数据与评估层 -->
            <div class="layer data-layer">
                <div class="layer-title">
                    📊 数据与评估层 - 持续优化反馈
                </div>
                <div class="layer-content">
                    <div class="component">
                        <div class="component-title">📈 效果评估</div>
                        <div class="component-items">
                            • F1 Score评估<br>
                            • Recall召回率<br>
                            • Accuracy准确率<br>
                            • 莱文斯坦距离<br>
                            • Loss损失函数
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">📊 数据分析</div>
                        <div class="component-items">
                            • 用户行为分析<br>
                            • 效率提升统计<br>
                            • 质量改进跟踪<br>
                            • 成本效益分析<br>
                            • 趋势预测分析
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">🔄 反馈优化</div>
                        <div class="component-items">
                            • 用户反馈收集<br>
                            • 模型效果监控<br>
                            • 自动化调优<br>
                            • A/B测试验证<br>
                            • 持续改进循环
                        </div>
                    </div>
                    <div class="component">
                        <div class="component-title">💾 知识沉淀</div>
                        <div class="component-items">
                            • 最佳实践提取<br>
                            • 经验案例归档<br>
                            • 知识图谱构建<br>
                            • 私域知识共享<br>
                            • 组织学习促进
                        </div>
                    </div>
                </div>
                <div class="key-feature">
                    🎯 通过持续评估与优化，实现AI能力的螺旋式提升
                </div>
            </div>
        </div>
    </div>
</body>
</html>
