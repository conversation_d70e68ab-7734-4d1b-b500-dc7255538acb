<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效技术方案流程图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            padding: 20px;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .title {
            font-size: 26px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 12px;
        }

        .core-concept {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            font-size: 14px;
        }

        /* 流程图布局 */
        .flowchart {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin-top: 25px;
            position: relative;
        }

        /* 流程节点样式 */
        .flow-node {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border: 3px solid transparent;
            position: relative;
            min-width: 300px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .flow-node:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        /* 开始节点 */
        .start-node {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #5a67d8;
            border-radius: 50px;
            min-width: 250px;
        }

        /* 决策节点 */
        .decision-node {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-color: #ff6b9d;
            transform: rotate(45deg);
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: auto;
        }

        .decision-content {
            transform: rotate(-45deg);
            text-align: center;
        }

        /* 处理节点 */
        .process-node {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            border-color: #38f9d7;
        }

        /* 数据节点 */
        .data-node {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            border-color: #fa709a;
            border-radius: 15px 50px 15px 50px;
        }

        /* 结束节点 */
        .end-node {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #5a67d8;
            border-radius: 50px;
            min-width: 250px;
        }

        .node-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .node-content {
            font-size: 12px;
            line-height: 1.5;
            opacity: 0.9;
        }

        /* 连接线和箭头 */
        .flow-arrow {
            width: 3px;
            height: 40px;
            background: linear-gradient(to bottom, #3498db, #e74c3c);
            position: relative;
            margin: -10px 0;
        }

        .flow-arrow::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 12px solid #e74c3c;
        }

        /* 分支箭头 */
        .branch-container {
            display: flex;
            justify-content: space-around;
            width: 100%;
            max-width: 800px;
            position: relative;
        }

        .branch-arrow {
            width: 2px;
            height: 60px;
            background: #3498db;
            position: relative;
        }

        .branch-arrow::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 10px solid #3498db;
        }

        .branch-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            white-space: nowrap;
        }

        /* 并行流程容器 */
        .parallel-container {
            display: flex;
            justify-content: space-around;
            width: 100%;
            max-width: 1200px;
            gap: 30px;
            flex-wrap: wrap;
        }

        /* 特殊标识 */
        .ai-badge {
            position: absolute;
            top: -12px;
            right: -12px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        /* 反馈循环线 */
        .feedback-loop {
            position: absolute;
            left: -100px;
            top: 50%;
            width: 80px;
            height: 200px;
            border: 3px solid #e74c3c;
            border-right: none;
            border-radius: 40px 0 0 40px;
            transform: translateY(-50%);
        }

        .feedback-loop::after {
            content: '🔄 反馈循环';
            position: absolute;
            left: -60px;
            top: 50%;
            transform: translateY(-50%) rotate(-90deg);
            background: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            white-space: nowrap;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .parallel-container {
                flex-direction: column;
                align-items: center;
                gap: 20px;
            }
            .branch-container {
                flex-direction: column;
                align-items: center;
                gap: 20px;
            }
            .feedback-loop {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            .flow-node {
                min-width: 250px;
                padding: 15px;
            }
            .decision-node {
                width: 150px;
                height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">测试域AI提效技术方案流程图</div>
            <div class="subtitle">基于上下文工程的智能执行流程</div>
            <div class="core-concept">
                私域经验 → 提示词模板 → 动态上下文 → 智能执行 → 知识沉淀
            </div>
        </div>

        <div class="flowchart">
            <!-- 开始节点 -->
            <div class="flow-node start-node">
                <div class="node-title">🚀 开始</div>
                <div class="node-content">用户输入业务需求<br>（文本/文件/图片）</div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 场景识别决策节点 -->
            <div class="flow-node decision-node">
                <div class="decision-content">
                    <div class="node-title">🎯 场景识别</div>
                    <div class="node-content">识别业务场景类型</div>
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 业务场景分支 -->
            <div class="branch-container">
                <div class="branch-arrow">
                    <div class="branch-label">用例设计</div>
                </div>
                <div class="branch-arrow">
                    <div class="branch-label">缺陷分析</div>
                </div>
                <div class="branch-arrow">
                    <div class="branch-label">测试报告</div>
                </div>
                <div class="branch-arrow">
                    <div class="branch-label">自动化脚本</div>
                </div>
            </div>

            <!-- 并行处理节点 -->
            <div class="parallel-container">
                <div class="flow-node data-node">
                    <div class="node-title">� 提示词模板</div>
                    <div class="node-content">
                        • 场景化模板选择<br>
                        • 执行动作流定义<br>
                        • 工具调用指令<br>
                        • 参数动态注入
                    </div>
                </div>
                <div class="flow-node data-node">
                    <div class="node-title">🧠 任务规划</div>
                    <div class="node-content">
                        • SequentialThinking MCP<br>
                        • 任务分解<br>
                        • 步骤规划<br>
                        • 风险识别
                    </div>
                </div>
                <div class="flow-node data-node">
                    <div class="node-title">� 上下文收集</div>
                    <div class="node-content">
                        • 网页检索<br>
                        • RAG知识库<br>
                        • 用户画像<br>
                        • 记忆系统
                    </div>
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- MCP工具调用节点 -->
            <div class="flow-node process-node">
                <div class="ai-badge">🧠</div>
                <div class="node-title">🛠️ MCP工具调用</div>
                <div class="node-content">
                    • FileSystem MCP：文件操作<br>
                    • iCenter MCP：系统集成<br>
                    • RDC MCP：远程调试<br>
                    • 自动化框架MCP：脚本执行
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 智能执行节点 -->
            <div class="flow-node process-node">
                <div class="node-title">⚡ 智能执行</div>
                <div class="node-content">
                    • 基于上下文智能推理<br>
                    • 调用工具完成任务<br>
                    • 结果格式化处理<br>
                    • 质量检查验证
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 用户交互决策节点 -->
            <div class="flow-node decision-node">
                <div class="decision-content">
                    <div class="node-title">🔄 用户反馈</div>
                    <div class="node-content">结果是否满意？</div>
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 分支：满意/不满意 -->
            <div class="branch-container">
                <div class="branch-arrow">
                    <div class="branch-label">不满意</div>
                </div>
                <div class="branch-arrow">
                    <div class="branch-label">满意</div>
                </div>
            </div>

            <!-- 并行结果处理 -->
            <div class="parallel-container">
                <div class="flow-node data-node" style="position: relative;">
                    <div class="node-title">🔄 迭代优化</div>
                    <div class="node-content">
                        • 收集用户反馈<br>
                        • 调整执行策略<br>
                        • 重新生成结果<br>
                        • 返回执行流程
                    </div>
                    <div class="feedback-loop"></div>
                </div>
                <div class="flow-node process-node">
                    <div class="node-title">✅ 结果确认</div>
                    <div class="node-content">
                        • 最终结果交付<br>
                        • 用户满意确认<br>
                        • 执行记录保存<br>
                        • 进入知识沉淀
                    </div>
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 知识沉淀节点 -->
            <div class="flow-node data-node">
                <div class="node-title">💾 Memory MCP记忆</div>
                <div class="node-content">
                    • 对话过程实时记录<br>
                    • 关键决策点标记<br>
                    • 用户偏好学习<br>
                    • 成功案例提取
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- RAG同步节点 -->
            <div class="flow-node process-node">
                <div class="node-title">📚 RAG知识库同步</div>
                <div class="node-content">
                    • 定期同步Memory内容<br>
                    • 知识结构化处理<br>
                    • 向量化存储<br>
                    • 检索索引更新
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 知识优化节点 -->
            <div class="parallel-container">
                <div class="flow-node data-node">
                    <div class="node-title">🔄 模板优化</div>
                    <div class="node-content">
                        • 基于效果反馈<br>
                        • 模板参数调优<br>
                        • 新场景模板生成<br>
                        • 版本管理
                    </div>
                </div>
                <div class="flow-node data-node">
                    <div class="node-title">🌐 知识共享</div>
                    <div class="node-content">
                        • 私域知识去敏化<br>
                        • 最佳实践提炼<br>
                        • 团队经验共享<br>
                        • 持续学习促进
                    </div>
                </div>
            </div>

            <div class="flow-arrow"></div>

            <!-- 结束节点 -->
            <div class="flow-node end-node">
                <div class="node-title">🎯 完成</div>
                <div class="node-content">任务完成，知识沉淀<br>等待下一次调用</div>
            </div>
        </div>

        <!-- 详细流程说明 -->
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea;">
            <h3 style="color: #2c3e50; margin-bottom: 10px; font-size: 16px;">🔄 完整执行流程示例：用例设计场景</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; font-size: 12px;">
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #667eea; margin-bottom: 8px;">📋 1. 场景触发</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        用户在IDE中输入："帮我设计登录功能的测试用例"<br>
                        系统识别为用例设计场景，调用对应提示词模板
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #f093fb; margin-bottom: 8px;">🧠 2. 智能规划</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        SequentialThinking MCP分析任务：<br>
                        • 需求理解 → 场景分析 → 用例设计 → 结果验证<br>
                        • 识别需要的上下文信息和工具
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #43e97b; margin-bottom: 8px;">🔍 3. 上下文收集</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        • RAG检索：历史登录测试用例<br>
                        • 网页检索：登录功能测试最佳实践<br>
                        • 用户画像：技术栈偏好、经验水平<br>
                        • FileSystem MCP：获取相关需求文档
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #fa709a; margin-bottom: 8px;">✨ 4. 智能生成</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        基于收集的上下文，生成包含：<br>
                        • 正常流程用例<br>
                        • 异常流程用例<br>
                        • 边界条件测试<br>
                        • 安全性测试用例
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #667eea; margin-bottom: 8px;">🔄 5. 交互优化</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        用户通过文本反馈："增加多设备登录的测试场景"<br>
                        系统基于反馈调整输出，直到满足要求
                    </p>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="color: #43e97b; margin-bottom: 8px;">💾 6. 知识沉淀</h4>
                    <p style="margin: 0; line-height: 1.5; color: #555;">
                        Memory MCP记录成功案例，定期同步到RAG<br>
                        优化登录测试用例模板，供团队复用
                    </p>
                </div>
            </div>
        </div>

        <!-- 技术优势说明 -->
        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white;">
            <h3 style="margin-bottom: 10px; font-size: 16px;">🚀 技术方案优势</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 12px; font-size: 11px;">
                <div style="background: rgba(255,255,255,0.15); padding: 10px; border-radius: 6px;">
                    <strong>🎯 精准匹配</strong><br>
                    基于业务场景的提示词模板，确保输出结果符合实际需求
                </div>
                <div style="background: rgba(255,255,255,0.15); padding: 10px; border-radius: 6px;">
                    <strong>🧠 智能推理</strong><br>
                    SequentialThinking MCP提供结构化思考，提升任务执行质量
                </div>
                <div style="background: rgba(255,255,255,0.15); padding: 10px; border-radius: 6px;">
                    <strong>🔍 丰富上下文</strong><br>
                    多源信息融合，为AI提供充分的决策依据
                </div>
                <div style="background: rgba(255,255,255,0.15); padding: 10px; border-radius: 6px;">
                    <strong>🔄 持续优化</strong><br>
                    用户反馈驱动的迭代改进，确保输出质量
                </div>
                <div style="background: rgba(255,255,255,0.15); padding: 10px; border-radius: 6px;">
                    <strong>💾 知识复用</strong><br>
                    私域经验自动沉淀，团队知识共享增值
                </div>
                <div style="background: rgba(255,255,255,0.15); padding: 10px; border-radius: 6px;">
                    <strong>🛠️ 工具集成</strong><br>
                    MCP工具链无缝集成，扩展AI能力边界
                </div>
            </div>
        </div>
    </div>
</body>
</html>
